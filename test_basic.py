#!/usr/bin/env python3
"""
Basic test script to verify logo_cli.py functionality
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly."""
    try:
        import logo_cli
        print("✓ Successfully imported logo_cli module")
        return True
    except Exception as e:
        print(f"✗ Failed to import logo_cli: {e}")
        return False

def test_basic_classes():
    """Test that basic classes can be instantiated."""
    try:
        from logo_cli import AIProvider, ProcessingTier, EnhancedGenerationRequest
        
        # Test enums
        provider = AIProvider.ENSEMBLE
        tier = ProcessingTier.STANDARD
        print(f"✓ Enums work: {provider.value}, {tier.value}")
        
        # Test data class
        request = EnhancedGenerationRequest(
            prompt="Test logo for tech startup",
            provider=provider,
            processing_tier=tier
        )
        print(f"✓ EnhancedGenerationRequest created: {request.prompt}")
        
        return True
    except Exception as e:
        print(f"✗ Failed to test basic classes: {e}")
        return False

def test_database():
    """Test database functionality."""
    try:
        from logo_cli import EnhancedDatabaseManager
        
        db = EnhancedDatabaseManager()
        print("✓ Database manager created successfully")
        return True
    except Exception as e:
        print(f"✗ Failed to test database: {e}")
        return False

def test_ai_providers():
    """Test AI provider classes."""
    try:
        from logo_cli import BaseAIProvider, MidjourneyProvider, DallE3Provider
        
        provider = MidjourneyProvider("test_api_key")
        print("✓ AI provider classes work")
        return True
    except Exception as e:
        print(f"✗ Failed to test AI providers: {e}")
        return False

def main():
    """Run all tests."""
    print("Running basic functionality tests for Logo Showdown...")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_basic_classes,
        test_database,
        test_ai_providers
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Logo Showdown is ready to use.")
        return 0
    else:
        print("❌ Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
