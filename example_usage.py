#!/usr/bin/env python3
"""
Example usage of Logo Showdown platform
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from logo_cli import (
    EnhancedGenerationRequest, 
    AIProvider, 
    ProcessingTier,
    EnhancedDatabaseManager,
    EnterpriseAIManager,
    EnhancedImageAnalyzer,
    PredictiveAnalytics
)

async def example_logo_generation():
    """Example of generating logos using the platform."""
    print("🎨 Logo Showdown - Example Usage")
    print("=" * 50)
    
    # Initialize components
    print("Initializing platform components...")
    db = EnhancedDatabaseManager()
    ai_manager = EnterpriseAIManager(db)
    
    # Create generation request
    request = EnhancedGenerationRequest(
        prompt="Modern minimalist logo for a tech startup specializing in AI",
        provider=AIProvider.ENSEMBLE,
        processing_tier=ProcessingTier.PREMIUM,
        batch_size=3,
        target_audience="Tech professionals and investors",
        brand_values="Innovation, reliability, cutting-edge technology"
    )
    
    print(f"\n📝 Generation Request:")
    print(f"   Prompt: {request.prompt}")
    print(f"   Provider: {request.provider.value}")
    print(f"   Tier: {request.processing_tier.value}")
    print(f"   Batch Size: {request.batch_size}")
    
    # Generate logos
    print(f"\n🤖 Generating logos with AI ensemble...")
    result = await ai_manager.ensemble_generate(request)
    
    if result.get("success"):
        images = result.get("images", [])
        print(f"✅ Successfully generated {len(images)} logos!")
        print(f"💰 Total cost: ${result.get('cost', 0):.2f}")
        print(f"🔧 Providers used: {', '.join(result.get('providers_used', []))}")
        print(f"🎯 Ensemble confidence: {result.get('ensemble_confidence', 0):.2f}")
        
        print(f"\n📊 Generated Logos:")
        for i, img in enumerate(images, 1):
            print(f"   {i}. {img.get('url', 'N/A')} (Score: {img.get('ensemble_score', 0):.2f})")
    else:
        print(f"❌ Generation failed: {result.get('error', 'Unknown error')}")

async def example_predictive_analytics():
    """Example of using predictive analytics."""
    print(f"\n🔮 Predictive Analytics Example")
    print("-" * 30)
    
    db = EnhancedDatabaseManager()
    analytics = PredictiveAnalytics(db)
    
    # Mock logo data
    logo_data = {
        "prompt": "Modern tech startup logo",
        "provider": "ensemble",
        "color_palette": ["#2563eb", "#ffffff", "#1f2937"],
        "style_tags": ["minimalist", "modern", "tech"]
    }
    
    print("🧠 Analyzing logo success probability...")
    predictions = await analytics.predict_logo_success(logo_data)
    
    if "error" not in predictions:
        success_prob = predictions.get("success_probability", 0)
        confidence = predictions.get("confidence_level", 0)
        
        print(f"📈 Success Probability: {success_prob:.2%}")
        print(f"🎯 Confidence Level: {confidence:.2f}")
        
        recommendations = predictions.get("recommendations", [])
        if recommendations:
            print(f"\n💡 Recommendations:")
            for rec in recommendations:
                print(f"   • {rec}")
    else:
        print(f"❌ Prediction failed: {predictions.get('error')}")

async def example_market_trends():
    """Example of market trend analysis."""
    print(f"\n📊 Market Trends Analysis")
    print("-" * 30)
    
    db = EnhancedDatabaseManager()
    analytics = PredictiveAnalytics(db)
    
    print("🔍 Analyzing current market trends...")
    trends = await analytics.predict_market_trends(timeframe_days=30)
    
    print(f"⏰ Timeframe: {trends.get('timeframe', 'N/A')}")
    print(f"🎯 Confidence: {trends.get('confidence', 0):.2%}")
    
    emerging = trends.get("emerging_styles", [])
    if emerging:
        print(f"\n📈 Emerging Trends:")
        for trend in emerging:
            print(f"   • {trend}")
    
    declining = trends.get("declining_styles", [])
    if declining:
        print(f"\n📉 Declining Trends:")
        for trend in declining:
            print(f"   • {trend}")

async def main():
    """Run all examples."""
    try:
        await example_logo_generation()
        await example_predictive_analytics()
        await example_market_trends()
        
        print(f"\n🎉 All examples completed successfully!")
        print(f"\n💡 To use the CLI interface, run:")
        print(f"   python logo_cli.py --help")
        print(f"   python logo_cli.py generate --prompt 'Your logo description'")
        
    except Exception as e:
        print(f"❌ Example failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
